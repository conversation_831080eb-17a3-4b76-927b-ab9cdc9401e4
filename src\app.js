const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const passport = require('passport');
const { json, urlencoded } = express;
require('dotenv').config();

// 导入路由
const routes = require('./routes');
const { errorHandler, notFound } = require('./middlewares/error.middleware');

// 初始化应用
const app = express();

// 中间件
app.use(helmet()); // 安全头
app.use(morgan('dev')); // 日志记录
app.use(cors()); // 跨域设置
app.use(json()); // 解析JSON请求体
app.use(urlencoded({ extended: true })); // 解析URL编码请求体

// 初始化Passport
app.use(passport.initialize());
require('./config/passport');

// API路由
const apiPrefix = process.env.API_PREFIX || '/api/v1';
app.use(apiPrefix, routes);

// 404处理
app.use(notFound);

// 错误处理中间件
app.use(errorHandler);

module.exports = app;
