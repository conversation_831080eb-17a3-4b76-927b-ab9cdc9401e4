const express = require('express');
const router = express.Router();
const authController = require('../../controllers/auth/auth.controller');
const passport = require('passport');
const { validate, registerSchema, loginSchema } = require('../../middlewares/validation.middleware');
const { strictRateLimit, moderateRateLimit } = require('../../middlewares/rateLimit.middleware');

// 注册新用户
router.post('/register', strictRateLimit, validate(registerSchema), authController.register);

// 用户登录
router.post('/login', strictRateLimit, validate(loginSchema), authController.login);

// 刷新token
router.post('/refresh-token', moderateRateLimit, authController.refreshToken);

// 重置密码请求
router.post('/forgot-password', strictRateLimit, authController.forgotPassword);

// 重置密码
router.post('/reset-password', strictRateLimit, authController.resetPassword);

// 验证JWT Token (需要验证token的路由)
router.get('/me', passport.authenticate('jwt', { session: false }), authController.getProfile);

// 退出登录 (使JWT token失效)
router.post('/logout', passport.authenticate('jwt', { session: false }), authController.logout);

module.exports = router; 