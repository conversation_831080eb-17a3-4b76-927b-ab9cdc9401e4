const express = require('express');
const router = express.Router();
const userController = require('../../controllers/user/user.controller');
const passport = require('passport');
const { validate, updateUserSchema, paginationSchema, idParamSchema } = require('../../middlewares/validation.middleware');

// JWT 中间件 - 保护需要认证的路由
const authMiddleware = passport.authenticate('jwt', { session: false });

// 管理员检查中间件
const adminMiddleware = require('../../middlewares/admin.middleware');

// 公开路由
router.get('/', validate(paginationSchema, 'query'), userController.getAllUsers); // 获取所有用户列表

// 需要认证的路由
router.get('/:id', validate(idParamSchema, 'params'), authMiddleware, userController.getUserById); // 获取单个用户
router.put('/:id', validate(idParamSchema, 'params'), validate(updateUserSchema), authMiddleware, userController.updateUser); // 更新用户信息
router.delete('/:id', validate(idParamSchema, 'params'), authMiddleware, adminMiddleware, userController.deleteUser); // 删除用户 (仅管理员)

// 获取用户详细资料 (MongoDB)
router.get('/:id/profile', validate(idParamSchema, 'params'), authMiddleware, userController.getUserProfile);
router.put('/:id/profile', validate(idParamSchema, 'params'), authMiddleware, userController.updateUserProfile);

module.exports = router; 