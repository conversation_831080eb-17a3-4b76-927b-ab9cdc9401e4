/**
 * 基于内存的简单速率限制中间件
 * 生产环境建议使用Redis存储
 */

// 存储客户端请求记录
const clients = new Map();

/**
 * 创建速率限制中间件
 * @param {Object} options - 配置选项
 * @param {number} options.windowMs - 时间窗口（毫秒）
 * @param {number} options.max - 最大请求次数
 * @param {string} options.message - 超限时的错误消息
 * @param {Function} options.keyGenerator - 生成客户端标识的函数
 */
const createRateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15分钟
    max = 100, // 最大请求次数
    message = '请求过于频繁，请稍后再试',
    keyGenerator = (req) => req.ip || 'unknown'
  } = options;

  return (req, res, next) => {
    const key = keyGenerator(req);
    const now = Date.now();
    
    // 获取或创建客户端记录
    if (!clients.has(key)) {
      clients.set(key, {
        count: 0,
        resetTime: now + windowMs
      });
    }
    
    const client = clients.get(key);
    
    // 检查是否需要重置计数器
    if (now > client.resetTime) {
      client.count = 0;
      client.resetTime = now + windowMs;
    }
    
    // 增加请求计数
    client.count++;
    
    // 设置响应头
    res.set({
      'X-RateLimit-Limit': max,
      'X-RateLimit-Remaining': Math.max(0, max - client.count),
      'X-RateLimit-Reset': new Date(client.resetTime).toISOString()
    });
    
    // 检查是否超过限制
    if (client.count > max) {
      return res.status(429).json({
        status: 'error',
        message,
        retryAfter: Math.ceil((client.resetTime - now) / 1000)
      });
    }
    
    next();
  };
};

/**
 * 清理过期的客户端记录
 */
const cleanupExpiredClients = () => {
  const now = Date.now();
  for (const [key, client] of clients.entries()) {
    if (now > client.resetTime + 60000) { // 额外等待1分钟再清理
      clients.delete(key);
    }
  }
};

// 每5分钟清理一次过期记录
setInterval(cleanupExpiredClients, 5 * 60 * 1000);

/**
 * 预定义的速率限制配置
 */

// 严格限制（登录、注册等敏感操作）
const strictRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次请求
  message: '操作过于频繁，请15分钟后再试'
});

// 中等限制（API调用）
const moderateRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最多100次请求
  message: '请求过于频繁，请稍后再试'
});

// 宽松限制（静态资源等）
const lenientRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 最多1000次请求
  message: '请求过于频繁，请稍后再试'
});

// 基于用户ID的速率限制
const createUserRateLimit = (options = {}) => {
  return createRateLimit({
    ...options,
    keyGenerator: (req) => {
      // 优先使用用户ID，否则使用IP
      return req.user?.id?.toString() || req.ip || 'unknown';
    }
  });
};

// 消息发送限制
const messageRateLimit = createUserRateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 30, // 最多30条消息
  message: '消息发送过于频繁，请稍后再试'
});

// 文件上传限制
const uploadRateLimit = createUserRateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 50, // 最多50个文件
  message: '文件上传过于频繁，请稍后再试'
});

module.exports = {
  createRateLimit,
  strictRateLimit,
  moderateRateLimit,
  lenientRateLimit,
  createUserRateLimit,
  messageRateLimit,
  uploadRateLimit
};
