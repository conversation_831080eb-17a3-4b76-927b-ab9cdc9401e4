{"name": "elitehub-backend", "version": "1.0.0", "description": "EliteHub后端服务", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^5.1.0", "ioredis": "^5.6.1", "mongoose": "^8.16.4", "mysql2": "^3.14.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "stripe": "^18.3.0", "cors": "^2.8.5", "dotenv": "^16.4.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.3", "jest": "^29.7.0"}}