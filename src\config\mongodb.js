require('dotenv').config();
const mongoose = require('mongoose');

// MongoDB配置选项
const mongooseOptions = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  maxPoolSize: 10, // 连接池最大连接数
  serverSelectionTimeoutMS: 5000, // 服务器选择超时时间
  socketTimeoutMS: 45000, // Socket超时时间
  family: 4, // 使用IPv4
  bufferCommands: false, // 禁用mongoose缓冲
  bufferMaxEntries: 0 // 禁用mongoose缓冲
};

// 连接字符串
const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/elitehub';

// 连接事件处理
mongoose.connection.on('connected', () => {
  console.log('MongoDB连接成功');
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB连接错误:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('MongoDB连接断开');
});

// 优雅关闭
process.on('SIGINT', async () => {
  await mongoose.connection.close();
  console.log('MongoDB连接已关闭');
  process.exit(0);
});

// 导出连接函数
const connectMongoDB = async () => {
  try {
    await mongoose.connect(mongoURI, mongooseOptions);
    return mongoose.connection;
  } catch (error) {
    console.error('MongoDB连接失败:', error);
    throw error;
  }
};

module.exports = {
  connectMongoDB,
  mongoose
};