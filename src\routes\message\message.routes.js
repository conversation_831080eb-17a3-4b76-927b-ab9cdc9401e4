const express = require('express');
const router = express.Router();
const messageController = require('../../controllers/message/message.controller');
const passport = require('passport');
const { validate, paginationSchema, idParamSchema } = require('../../middlewares/validation.middleware');

// JWT 中间件 - 所有消息相关路由都需要认证
const authMiddleware = passport.authenticate('jwt', { session: false });

// 获取用户的所有对话列表
router.get('/conversations', 
  validate(paginationSchema, 'query'), 
  authMiddleware, 
  messageController.getConversations
);

// 获取特定对话的消息历史
router.get('/conversations/:conversationId/messages', 
  validate(idParamSchema, 'params'), 
  validate(paginationSchema, 'query'), 
  authMiddleware, 
  messageController.getConversationMessages
);

// 获取两个用户之间的对话消息
router.get('/users/:targetUserId/messages', 
  validate(idParamSchema, 'params'), 
  validate(paginationSchema, 'query'), 
  authMiddleware, 
  messageController.getUserConversation
);

// 标记消息为已读
router.post('/mark-read', 
  authMiddleware, 
  messageController.markMessagesAsRead
);

// 删除消息
router.delete('/:messageId', 
  validate(idParamSchema, 'params'), 
  authMiddleware, 
  messageController.deleteMessage
);

// 获取未读消息数量
router.get('/unread-count', 
  authMiddleware, 
  messageController.getUnreadCount
);

module.exports = router;
