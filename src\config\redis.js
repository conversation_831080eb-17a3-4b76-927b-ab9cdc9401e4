require('dotenv').config();
const Redis = require('ioredis');

// Redis配置选项
const redisOptions = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || '',
  db: process.env.REDIS_DB || 0,
  connectTimeout: 10000,
  lazyConnect: true,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxLoadingTimeout: 0,
  retryStrategy: (times) => {
    // 重试策略：最多重试10次
    if (times > 10) {
      return null;
    }
    const delay = Math.min(times * 50, 2000);
    return delay;
  }
};

// 创建Redis客户端
const createRedisClient = () => {
  const client = new Redis(redisOptions);

  client.on('error', (err) => {
    console.error('Redis连接错误:', err);
  });

  client.on('connect', () => {
    console.log('Redis连接成功');
  });

  client.on('ready', () => {
    console.log('Redis准备就绪');
  });

  client.on('close', () => {
    console.log('Redis连接关闭');
  });

  client.on('reconnecting', () => {
    console.log('Redis重新连接中...');
  });

  return client;
};

// 创建Redis发布/订阅客户端
const createRedisPubSubClient = () => {
  const pubClient = createRedisClient();
  const subClient = createRedisClient();

  return { pubClient, subClient };
};

module.exports = {
  createRedisClient,
  createRedisPubSubClient
};