const http = require('http');
const socketIo = require('socket.io');
const app = require('./app');
require('dotenv').config();
const { sequelize } = require('./models');
const { connectMongoDB } = require('./config/mongodb');
const { createRedisClient } = require('./config/redis');
const MessageService = require('./services/message.service');

// 创建HTTP服务器
const server = http.createServer(app);

// 配置Socket.IO
const io = socketIo(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || '*', // 生产环境中应替换为具体域名
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
});

// 创建Redis客户端
const redisClient = createRedisClient();

// 将Redis客户端和Socket.IO添加到app对象以便全局访问
app.set('redis', redisClient);
app.set('io', io);

// 初始化消息服务
const messageService = new MessageService(io, redisClient);
messageService.initialize();

// 端口设置
const PORT = process.env.PORT || 3000;

// 数据库连接和服务器启动
const startServer = async () => {
  try {
    // 连接MySQL (通过Sequelize)
    await sequelize.authenticate();
    console.log('MySQL数据库连接成功');
    
    // 同步模型到数据库（开发环境可使用）
    // 生产环境应使用迁移而非sync
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('数据库模型同步成功');
    }

    // 连接MongoDB
    await connectMongoDB();

    // 启动服务器
    server.listen(PORT, () => {
      console.log(`服务器运行在 http://localhost:${PORT}`);
      console.log(`API接口前缀: ${process.env.API_PREFIX || '/api/v1'}`);
      console.log(`环境: ${process.env.NODE_ENV}`);
    });
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
};

// 处理未捕获异常
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
  process.exit(1);
});

process.on('unhandledRejection', (err) => {
  console.error('未处理的Promise拒绝:', err);
  process.exit(1);
});

startServer();
