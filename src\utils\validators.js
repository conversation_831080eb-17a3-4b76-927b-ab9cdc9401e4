const Joi = require('joi');

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否有效
 */
const isValidEmail = (email) => {
  const emailSchema = Joi.string().email();
  const { error } = emailSchema.validate(email);
  return !error;
};

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @returns {Object} 验证结果
 */
const validatePasswordStrength = (password) => {
  const result = {
    isValid: true,
    errors: []
  };

  if (!password || password.length < 6) {
    result.isValid = false;
    result.errors.push('密码至少需要6个字符');
  }

  if (password && password.length > 100) {
    result.isValid = false;
    result.errors.push('密码不能超过100个字符');
  }

  if (!/(?=.*[a-z])/.test(password)) {
    result.isValid = false;
    result.errors.push('密码必须包含至少一个小写字母');
  }

  if (!/(?=.*[A-Z])/.test(password)) {
    result.isValid = false;
    result.errors.push('密码必须包含至少一个大写字母');
  }

  if (!/(?=.*\d)/.test(password)) {
    result.isValid = false;
    result.errors.push('密码必须包含至少一个数字');
  }

  return result;
};

/**
 * 验证用户名格式
 * @param {string} username - 用户名
 * @returns {Object} 验证结果
 */
const validateUsername = (username) => {
  const result = {
    isValid: true,
    errors: []
  };

  if (!username) {
    result.isValid = false;
    result.errors.push('用户名是必填项');
    return result;
  }

  if (username.length < 3) {
    result.isValid = false;
    result.errors.push('用户名至少需要3个字符');
  }

  if (username.length > 50) {
    result.isValid = false;
    result.errors.push('用户名不能超过50个字符');
  }

  if (!/^[a-zA-Z0-9]+$/.test(username)) {
    result.isValid = false;
    result.errors.push('用户名只能包含字母和数字');
  }

  return result;
};

/**
 * 验证文件类型
 * @param {string} filename - 文件名
 * @param {Array} allowedTypes - 允许的文件类型
 * @returns {boolean} 是否有效
 */
const isValidFileType = (filename, allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']) => {
  if (!filename) return false;
  
  const extension = filename.split('.').pop().toLowerCase();
  return allowedTypes.includes(extension);
};

/**
 * 验证文件大小
 * @param {number} fileSize - 文件大小（字节）
 * @param {number} maxSize - 最大允许大小（字节）
 * @returns {boolean} 是否有效
 */
const isValidFileSize = (fileSize, maxSize = 10485760) => { // 默认10MB
  return fileSize && fileSize > 0 && fileSize <= maxSize;
};

/**
 * 验证URL格式
 * @param {string} url - URL地址
 * @returns {boolean} 是否有效
 */
const isValidUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * 验证手机号格式（中国大陆）
 * @param {string} phone - 手机号
 * @returns {boolean} 是否有效
 */
const isValidPhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 验证身份证号格式（中国大陆）
 * @param {string} idCard - 身份证号
 * @returns {boolean} 是否有效
 */
const isValidIdCard = (idCard) => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
};

/**
 * 清理和验证HTML内容
 * @param {string} content - HTML内容
 * @returns {string} 清理后的内容
 */
const sanitizeHtml = (content) => {
  if (!content) return '';
  
  // 移除潜在危险的HTML标签和属性
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim();
};

/**
 * 验证分页参数
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @returns {Object} 验证结果和标准化的参数
 */
const validatePagination = (page, limit) => {
  const normalizedPage = Math.max(1, parseInt(page) || 1);
  const normalizedLimit = Math.min(100, Math.max(1, parseInt(limit) || 20));
  
  return {
    page: normalizedPage,
    limit: normalizedLimit,
    offset: (normalizedPage - 1) * normalizedLimit
  };
};

/**
 * 验证排序参数
 * @param {string} sortBy - 排序字段
 * @param {string} sortOrder - 排序方向
 * @param {Array} allowedFields - 允许的排序字段
 * @returns {Object} 验证结果
 */
const validateSort = (sortBy, sortOrder, allowedFields = []) => {
  const validSortBy = allowedFields.includes(sortBy) ? sortBy : 'created_at';
  const validSortOrder = ['asc', 'desc'].includes(sortOrder?.toLowerCase()) 
    ? sortOrder.toLowerCase() 
    : 'desc';
  
  return {
    sortBy: validSortBy,
    sortOrder: validSortOrder
  };
};

module.exports = {
  isValidEmail,
  validatePasswordStrength,
  validateUsername,
  isValidFileType,
  isValidFileSize,
  isValidUrl,
  isValidPhone,
  isValidIdCard,
  sanitizeHtml,
  validatePagination,
  validateSort
};
