const Joi = require('joi');

/**
 * 通用验证中间件
 * @param {Object} schema - Joi验证模式
 * @param {string} property - 要验证的属性 ('body', 'query', 'params')
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false, // 返回所有错误
      allowUnknown: false, // 不允许未知字段
      stripUnknown: true // 移除未知字段
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.status(400).json({
        status: 'error',
        message: '数据验证失败',
        errors: errorDetails
      });
    }

    // 将验证后的数据替换原始数据
    req[property] = value;
    next();
  };
};

/**
 * 用户注册验证模式
 */
const registerSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过50个字符',
      'any.required': '用户名是必填项'
    }),
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    }),
  password: Joi.string()
    .min(6)
    .max(100)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)'))
    .required()
    .messages({
      'string.min': '密码至少需要6个字符',
      'string.max': '密码不能超过100个字符',
      'string.pattern.base': '密码必须包含至少一个大写字母、一个小写字母和一个数字',
      'any.required': '密码是必填项'
    })
});

/**
 * 用户登录验证模式
 */
const loginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    }),
  password: Joi.string()
    .required()
    .messages({
      'any.required': '密码是必填项'
    })
});

/**
 * 用户更新验证模式
 */
const updateUserSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .optional()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过50个字符'
    }),
  email: Joi.string()
    .email()
    .optional()
    .messages({
      'string.email': '请输入有效的邮箱地址'
    }),
  avatar: Joi.string()
    .uri()
    .optional()
    .messages({
      'string.uri': '头像必须是有效的URL地址'
    }),
  role: Joi.string()
    .valid('user', 'admin', 'superadmin')
    .optional()
    .messages({
      'any.only': '角色只能是user、admin或superadmin'
    }),
  status: Joi.string()
    .valid('active', 'disabled', 'pending')
    .optional()
    .messages({
      'any.only': '状态只能是active、disabled或pending'
    })
});

/**
 * 消息发送验证模式
 */
const sendMessageSchema = Joi.object({
  recipientId: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': '接收者ID必须是数字',
      'number.integer': '接收者ID必须是整数',
      'number.positive': '接收者ID必须是正数',
      'any.required': '接收者ID是必填项'
    }),
  content: Joi.string()
    .min(1)
    .max(5000)
    .required()
    .messages({
      'string.min': '消息内容不能为空',
      'string.max': '消息内容不能超过5000个字符',
      'any.required': '消息内容是必填项'
    }),
  type: Joi.string()
    .valid('text', 'image', 'file', 'system')
    .default('text')
    .messages({
      'any.only': '消息类型只能是text、image、file或system'
    }),
  fileUrl: Joi.string()
    .uri()
    .when('type', {
      is: Joi.valid('image', 'file'),
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'string.uri': '文件URL必须是有效的URL地址',
      'any.required': '当消息类型为image或file时，文件URL是必填项'
    }),
  fileName: Joi.string()
    .max(255)
    .when('type', {
      is: 'file',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'string.max': '文件名不能超过255个字符',
      'any.required': '当消息类型为file时，文件名是必填项'
    }),
  fileSize: Joi.number()
    .integer()
    .positive()
    .max(10485760) // 10MB
    .when('type', {
      is: Joi.valid('image', 'file'),
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'number.base': '文件大小必须是数字',
      'number.integer': '文件大小必须是整数',
      'number.positive': '文件大小必须是正数',
      'number.max': '文件大小不能超过10MB',
      'any.required': '当消息类型为image或file时，文件大小是必填项'
    })
});

/**
 * 分页查询验证模式
 */
const paginationSchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码必须大于0'
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20)
    .messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量必须大于0',
      'number.max': '每页数量不能超过100'
    })
});

/**
 * ID参数验证模式
 */
const idParamSchema = Joi.object({
  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID必须是数字',
      'number.integer': 'ID必须是整数',
      'number.positive': 'ID必须是正数',
      'any.required': 'ID是必填项'
    })
});

module.exports = {
  validate,
  registerSchema,
  loginSchema,
  updateUserSchema,
  sendMessageSchema,
  paginationSchema,
  idParamSchema
};
