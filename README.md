# EliteHub 后端服务

EliteHub 是一个基于 Node.js 的现代化后端服务，提供用户管理、实时消息和多数据库支持。

## 🚀 功能特性

- **用户管理系统** - 注册、登录、权限管理
- **实时消息系统** - 基于 Socket.IO 的私聊和群聊
- **多数据库架构** - MySQL + MongoDB + Redis
- **JWT 认证** - 安全的身份验证和授权
- **数据验证** - 完整的请求数据验证
- **速率限制** - 防止 API 滥用
- **错误处理** - 统一的错误处理机制

## 🛠️ 技术栈

- **Web 框架**: Express.js 5.1.0
- **数据库**: 
  - MySQL (Sequelize ORM) - 用户基础信息
  - MongoDB (Mongoose) - 消息、对话、用户详细资料
  - Redis - 缓存和会话管理
- **实时通信**: Socket.IO
- **认证**: Passport.js + JWT
- **数据验证**: Joi
- **安全**: Helmet, bcryptjs
- **支付**: Stripe (已集成)

## 📦 安装和配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd EliteHub
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
复制 `.env.example` 到 `.env` 并配置相应的环境变量：

```bash
cp .env.example .env
```

主要配置项：
- `NODE_ENV` - 运行环境 (development/production)
- `PORT` - 服务器端口
- `JWT_SECRET` - JWT 密钥
- `DB_*` - MySQL 数据库配置
- `MONGODB_URI` - MongoDB 连接字符串
- `REDIS_*` - Redis 配置

### 4. 数据库设置

#### MySQL
确保 MySQL 服务运行，并创建数据库：
```sql
CREATE DATABASE elitehub;
```

#### MongoDB
确保 MongoDB 服务运行。

#### Redis
确保 Redis 服务运行。

### 5. 启动服务

开发环境：
```bash
npm run dev
```

生产环境：
```bash
npm start
```

## 📡 API 接口

### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh-token` - 刷新令牌
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/logout` - 用户登出

### 用户管理
- `GET /api/v1/users` - 获取用户列表
- `GET /api/v1/users/:id` - 获取用户详情
- `PUT /api/v1/users/:id` - 更新用户信息
- `DELETE /api/v1/users/:id` - 删除用户（管理员）

### 消息系统
- `GET /api/v1/messages/conversations` - 获取对话列表
- `GET /api/v1/messages/users/:id/messages` - 获取与特定用户的消息
- `POST /api/v1/messages/mark-read` - 标记消息已读
- `GET /api/v1/messages/unread-count` - 获取未读消息数量

## 🔌 Socket.IO 事件

### 客户端发送
- `private-message` - 发送私聊消息
- `mark-read` - 标记消息已读
- `typing` - 正在输入
- `stop-typing` - 停止输入

### 服务端发送
- `connected` - 连接成功
- `new-message` - 新消息通知
- `message-sent` - 消息发送确认
- `user-typing` - 用户正在输入
- `user-stop-typing` - 用户停止输入
- `user-status-change` - 用户状态变化

## 🔒 安全特性

- **JWT 认证** - 所有敏感操作需要认证
- **数据验证** - 使用 Joi 进行请求数据验证
- **速率限制** - 防止 API 滥用
- **密码加密** - 使用 bcryptjs 加密存储
- **CORS 配置** - 跨域请求控制
- **Helmet** - 安全头设置

## 📁 项目结构

```
src/
├── config/          # 配置文件
├── controllers/     # 控制器
├── middlewares/     # 中间件
├── models/          # 数据模型
├── routes/          # 路由定义
├── services/        # 业务服务
├── utils/           # 工具函数
├── app.js           # Express 应用配置
├── server.js        # 服务器启动文件
└── index.js         # 入口文件
```

## 🚧 开发状态

项目目前处于基础搭建阶段，已完成：
- ✅ 基础架构搭建
- ✅ 用户认证系统
- ✅ 实时消息系统
- ✅ 数据验证中间件
- ✅ MongoDB 和 Socket.IO 配置

待开发功能：
- 🔄 文件上传功能
- 🔄 邮件服务
- 🔄 产品管理模块
- 🔄 订单管理模块
- 🔄 支付流程完善

## 📝 许可证

[MIT License](LICENSE)
